import type { ReactNode } from 'react';
import type { ViewA<PERSON>, ViewFilter, ViewSort, ViewPagination } from './views';

// Enhanced notification interface
export interface Notification {
  id: string;
  count: number;
  icon: ReactNode;
  type?: 'info' | 'warning' | 'error' | 'success';
  tooltip?: string;
  onClick?: () => void;
  badge?: {
    color?: string;
    pulse?: boolean;
  };
}

// Enhanced navigation link interface
export interface NavLink {
  id: string;
  label: string;
  href: string;
  isActive?: boolean;
  icon?: ReactNode;
  badge?: {
    text: string;
    color?: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
  onClick?: (event: React.MouseEvent) => void;
  isDisabled?: boolean;
  tooltip?: string;
  children?: NavLink[];
}

// Enhanced user interface
export interface User {
  id: string;
  name: string;
  email?: string;
  avatar: ReactNode;
  role?: string;
  status?: 'online' | 'offline' | 'away' | 'busy';
  notifications: Notification[];
  preferences?: {
    theme?: 'light' | 'dark' | 'system';
    language?: string;
    timezone?: string;
  };
  permissions?: string[];
}

// Enhanced app interface
export interface App {
  id: string;
  name: string;
  icon: ReactNode;
  color?: string;
  description?: string;
  version?: string;
  navLinks: NavLink[];
  breadcrumbs?: {
    label: string;
    href?: string;
    isActive?: boolean;
  }[];
  settings?: {
    showBreadcrumbs?: boolean;
    showNavigation?: boolean;
    maxNavLinks?: number;
  };
}

// Enhanced view mode interface
export interface ViewMode {
  id: string;
  name: string;
  icon: ReactNode;
  description?: string;
  isAvailable?: boolean;
  shortcut?: string;
  tooltip?: string;
  config?: any;
}

// Enhanced search interface
export interface SearchConfig {
  placeholder?: string;
  filters: ViewFilter[];
  sorts: ViewSort[];
  savedSearches?: {
    id: string;
    name: string;
    query: string;
    filters: ViewFilter[];
  }[];
  quickFilters?: ViewFilter[];
  onSearch: (query: string) => void;
  onFilterAdd?: (filter: ViewFilter) => void;
  onFilterRemove: (filterId: string) => void;
  onFilterUpdate?: (filterId: string, value: any) => void;
  onSortChange?: (sort: ViewSort) => void;
  onSavedSearchSelect?: (searchId: string) => void;
  onClearAll?: () => void;
  enableAdvancedSearch?: boolean;
  enableSavedSearches?: boolean;
}

// Enhanced view interface
export interface View {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  icon?: ReactNode;
  actions: ViewAction[];
  bulkActions?: ViewAction[];
  search: SearchConfig;
  pagination: ViewPagination;
  viewModes: ViewMode[];
  activeViewMode: string;
  selectedItems?: string[];
  totalItems?: number;
  settings?: {
    showSearch?: boolean;
    showPagination?: boolean;
    showViewModes?: boolean;
    showBulkActions?: boolean;
    showRefresh?: boolean;
    showExport?: boolean;
    showSettings?: boolean;
    autoRefresh?: {
      enabled: boolean;
      interval: number;
    };
  };
  onViewModeChange?: (viewModeId: string) => void;
  onRefresh?: () => void;
  onExport?: (format: 'csv' | 'excel' | 'pdf') => void;
  onSettings?: () => void;
  onBulkAction?: (actionId: string, selectedIds: string[]) => void;
}

// Enhanced layout configuration
export interface LayoutConfig {
  variant?: 'default' | 'compact' | 'minimal';
  showLogo?: boolean;
  showAppName?: boolean;
  showNavigation?: boolean;
  showUserMenu?: boolean;
  showNotifications?: boolean;
  showSearch?: boolean;
  showThemeToggle?: boolean;
  stickyHeader?: boolean;
  collapsibleNavigation?: boolean;
  mobileBreakpoint?: number;
  customStyles?: {
    backgroundColor?: string;
    borderColor?: string;
    textColor?: string;
    height?: string;
  };
}

// Enhanced responsive configuration
export interface ResponsiveConfig {
  mobile: {
    showHamburgerMenu?: boolean;
    showFAB?: boolean;
    hideSecondaryActions?: boolean;
    collapsedNavigation?: boolean;
    maxVisibleNavLinks?: number;
  };
  tablet: {
    showCompactView?: boolean;
    maxVisibleActions?: number;
  };
  desktop: {
    showFullView?: boolean;
    enableKeyboardShortcuts?: boolean;
  };
}

// Main enhanced props interface
export interface EnhancedDynamicAppHeaderProps {
  app: App;
  user: User;
  view: View;
  layout?: LayoutConfig;
  responsive?: ResponsiveConfig;
  className?: string;
  style?: React.CSSProperties;
  'data-testid'?: string;
  onNavigate?: (href: string) => void;
  onError?: (error: Error) => void;
  children?: ReactNode;
}

// Backward compatibility - simplified props (current interface)
export interface DynamicAppHeaderProps {
  app: {
    name: string;
    icon: ReactNode;
    navLinks: { label: string; href: string; isActive?: boolean }[];
  };
  user: {
    name: string;
    avatar: ReactNode;
    notifications: { count: number; icon: ReactNode }[];
  };
  view: {
    title: string;
    actions: { label: string; onClick: () => void; isPrimary?: boolean }[];
    search: {
      filters: { id: any; label: string }[];
      onSearch: (query: string) => void;
      onRemoveFilter: (id: any) => void;
    };
    pagination: { currentRange: string; onNext: () => void; onPrev: () => void };
    viewModes: { name: string; icon: ReactNode }[];
    activeViewMode: string;
  };
  className?: string;
  'data-testid'?: string;
}

// Helper type for props transformation
export type HeaderProps = DynamicAppHeaderProps | EnhancedDynamicAppHeaderProps;

// Type guard to check if props are enhanced
export function isEnhancedProps(props: HeaderProps): props is EnhancedDynamicAppHeaderProps {
  return 'layout' in props || 'responsive' in props || typeof (props.app as any).id === 'string';
}

// Props transformer utility
export function transformToEnhancedProps(props: DynamicAppHeaderProps): EnhancedDynamicAppHeaderProps {
  return {
    app: {
      id: 'app',
      name: props.app.name,
      icon: props.app.icon,
      navLinks: props.app.navLinks.map((link, index) => ({
        id: `nav-${index}`,
        label: link.label,
        href: link.href,
        isActive: link.isActive,
      })),
    },
    user: {
      id: 'user',
      name: props.user.name,
      avatar: props.user.avatar,
      notifications: props.user.notifications.map((notif, index) => ({
        id: `notif-${index}`,
        count: notif.count,
        icon: notif.icon,
      })),
    },
    view: {
      id: 'view',
      title: props.view.title,
      actions: props.view.actions.map((action, index) => ({
        id: `action-${index}`,
        label: action.label,
        onClick: action.onClick,
        isPrimary: action.isPrimary,
      })),
      search: {
        filters: props.view.search.filters.map(filter => ({
          id: filter.id,
          label: filter.label,
          value: filter.id,
        })),
        sorts: [],
        onSearch: props.view.search.onSearch,
        onFilterRemove: props.view.search.onRemoveFilter,
      },
      pagination: props.view.pagination,
      viewModes: props.view.viewModes.map((mode, index) => ({
        id: `mode-${index}`,
        name: mode.name,
        icon: mode.icon,
      })),
      activeViewMode: props.view.activeViewMode,
    },
    className: props.className,
    'data-testid': props['data-testid'],
  };
}
