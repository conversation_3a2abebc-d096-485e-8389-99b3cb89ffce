import React, { useState, useMemo } from 'react';
import { useTheme } from '../hooks/useTheme';
import { useViewStore } from '../stores/viewStore';
import DynamicAppHeader from '../components/layout/DynamicAppHeader';
import {
  ListView,
  GridView,
  CardView,
  KanbanView,
  CalendarView,
  PivotView,
  GraphView,
  ActivityView,
  MapView,
  GanttView,
  HierarchicalView,
  RelationshipView,
  FormView,
} from '../components/views';
import type {
  ViewConfig,
  KanbanViewConfig,
  CalendarViewConfig,
  PivotViewConfig,
  GraphViewConfig,
  ActivityViewConfig,
  MapViewConfig,
  GanttViewConfig,
  HierarchicalViewConfig,
  RelationshipViewConfig,
  FormViewConfig,
} from '../types/views';

// Sample data for demonstrations
const sampleData = [
  {
    id: '1',
    title: 'Sales Pipeline Review',
    description: 'Quarterly review of sales performance',
    status: 'In Progress',
    priority: 'High',
    assignee: '<PERSON>',
    startDate: '2024-01-15',
    endDate: '2024-01-30',
    progress: 75,
    department: 'Sales',
    revenue: 150000,
    latitude: 40.7128,
    longitude: -74.0060,
    type: 'meeting',
    parentId: null,
  },
  {
    id: '2',
    title: 'Product Launch Planning',
    description: 'Planning for Q2 product launch',
    status: 'Planning',
    priority: 'Medium',
    assignee: 'Jane Smith',
    startDate: '2024-02-01',
    endDate: '2024-03-15',
    progress: 25,
    department: 'Product',
    revenue: 200000,
    latitude: 34.0522,
    longitude: -118.2437,
    type: 'project',
    parentId: null,
  },
  {
    id: '3',
    title: 'Marketing Campaign',
    description: 'Digital marketing campaign for new product',
    status: 'Completed',
    priority: 'Low',
    assignee: 'Bob Johnson',
    startDate: '2024-01-01',
    endDate: '2024-01-20',
    progress: 100,
    department: 'Marketing',
    revenue: 75000,
    latitude: 41.8781,
    longitude: -87.6298,
    type: 'campaign',
    parentId: '2',
  },
];

export default function ViewsDemoPage() {
  const { colors } = useTheme();
  const [currentView, setCurrentView] = useState('list');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // View configurations
  const viewConfigs: { [key: string]: ViewConfig } = useMemo(() => ({
    kanban: {
      id: 'kanban',
      name: 'Kanban',
      icon: '📊',
      type: 'kanban',
      columns: [
        { id: 'Planning', title: 'Planning', color: '#3b82f6' },
        { id: 'In Progress', title: 'In Progress', color: '#f59e0b' },
        { id: 'Completed', title: 'Completed', color: '#10b981' },
      ],
      cardFields: ['title', 'assignee', 'priority'],
      groupByField: 'status',
    } as KanbanViewConfig,
    
    calendar: {
      id: 'calendar',
      name: 'Calendar',
      icon: '📅',
      type: 'calendar',
      dateField: 'startDate',
      endDateField: 'endDate',
      titleField: 'title',
      colorField: 'priority',
      viewModes: ['month', 'week', 'agenda'],
      defaultViewMode: 'month',
    } as CalendarViewConfig,
    
    pivot: {
      id: 'pivot',
      name: 'Pivot',
      icon: '#',
      type: 'pivot',
      rows: ['department'],
      columns: ['status'],
      values: [{ field: 'revenue', aggregation: 'sum', label: 'Total Revenue' }],
      showTotals: true,
    } as PivotViewConfig,
    
    graph: {
      id: 'graph',
      name: 'Graph',
      icon: '📈',
      type: 'graph',
      chartType: 'bar',
      xAxis: 'department',
      yAxis: 'revenue',
      aggregation: 'sum',
      showLegend: true,
      showGrid: true,
    } as GraphViewConfig,
    
    activity: {
      id: 'activity',
      name: 'Activity',
      icon: '🕒',
      type: 'activity',
      timestampField: 'startDate',
      titleField: 'title',
      descriptionField: 'description',
      userField: 'assignee',
      typeField: 'type',
      groupBy: 'date',
      showTimeline: true,
    } as ActivityViewConfig,
    
    map: {
      id: 'map',
      name: 'Map',
      icon: '🗺️',
      type: 'map',
      latitudeField: 'latitude',
      longitudeField: 'longitude',
      titleField: 'title',
      descriptionField: 'description',
      defaultZoom: 4,
      defaultCenter: [39.8283, -98.5795],
    } as MapViewConfig,
    
    gantt: {
      id: 'gantt',
      name: 'Gantt',
      icon: '🏗️',
      type: 'gantt',
      taskNameField: 'title',
      startDateField: 'startDate',
      endDateField: 'endDate',
      progressField: 'progress',
      timeScale: 'day',
    } as GanttViewConfig,
    
    hierarchical: {
      id: 'hierarchical',
      name: 'Tree',
      icon: '📦',
      type: 'hierarchical',
      idField: 'id',
      parentField: 'parentId',
      labelField: 'title',
      expandedByDefault: true,
      showLines: true,
    } as HierarchicalViewConfig,
    
    relationship: {
      id: 'relationship',
      name: 'Network',
      icon: '🔗',
      type: 'relationship',
      nodeIdField: 'id',
      nodeLabelField: 'title',
      edgeSourceField: 'parentId',
      edgeTargetField: 'id',
      layout: 'force',
      showLabels: true,
      enableZoom: true,
    } as RelationshipViewConfig,
    
    form: {
      id: 'form',
      name: 'Form',
      icon: '✍️',
      type: 'form',
      fields: [
        { name: 'title', label: 'Title', type: 'text', required: true },
        { name: 'description', label: 'Description', type: 'textarea' },
        { name: 'status', label: 'Status', type: 'select', required: true },
        { name: 'priority', label: 'Priority', type: 'select' },
        { name: 'assignee', label: 'Assignee', type: 'text' },
        { name: 'startDate', label: 'Start Date', type: 'date' },
        { name: 'endDate', label: 'End Date', type: 'date' },
        { name: 'progress', label: 'Progress', type: 'number' },
      ],
      layout: 'two-column',
    } as FormViewConfig,
  }), []);

  // Available view modes for header
  const viewModes = [
    { id: 'list', name: 'List', icon: '📋' },
    { id: 'grid', name: 'Grid', icon: '⊞' },
    { id: 'card', name: 'Card', icon: '🃏' },
    { id: 'kanban', name: 'Kanban', icon: '📊' },
    { id: 'calendar', name: 'Calendar', icon: '📅' },
    { id: 'pivot', name: 'Pivot', icon: '#' },
    { id: 'graph', name: 'Graph', icon: '📈' },
    { id: 'activity', name: 'Activity', icon: '🕒' },
    { id: 'map', name: 'Map', icon: '🗺️' },
    { id: 'gantt', name: 'Gantt', icon: '🏗️' },
    { id: 'hierarchical', name: 'Tree', icon: '📦' },
    { id: 'relationship', name: 'Network', icon: '🔗' },
    { id: 'form', name: 'Form', icon: '✍️' },
  ];

  // Header configuration
  const headerProps = {
    app: {
      name: 'Views Demo',
      icon: <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white font-bold">V</div>,
      navLinks: [
        { label: 'Dashboard', href: '/dashboard', isActive: false },
        { label: 'Views Demo', href: '/views-demo', isActive: true },
      ],
    },
    user: {
      name: 'Demo User',
      avatar: <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center text-white text-sm">DU</div>,
      notifications: [{ count: 3, icon: '🔔' }],
    },
    view: {
      title: `${viewModes.find(v => v.id === currentView)?.name || 'List'} View Demo`,
      actions: [
        { label: 'New Item', onClick: () => console.log('New item'), isPrimary: true },
        { label: 'Import', onClick: () => console.log('Import'), isPrimary: false },
        { label: 'Export', onClick: () => console.log('Export'), isPrimary: false },
      ],
      search: {
        filters: [
          { id: 'status', label: 'Status' },
          { id: 'priority', label: 'Priority' },
          { id: 'department', label: 'Department' },
        ],
        onSearch: (query: string) => console.log('Search:', query),
        onRemoveFilter: (id: any) => console.log('Remove filter:', id),
      },
      pagination: {
        currentRange: `1-${sampleData.length} / ${sampleData.length}`,
        onNext: () => console.log('Next page'),
        onPrev: () => console.log('Previous page'),
      },
      viewModes: viewModes.map(mode => ({ name: mode.name, icon: mode.icon })),
      activeViewMode: viewModes.find(v => v.id === currentView)?.name || 'List',
    },
  };

  const renderCurrentView = () => {
    const commonProps = {
      data: sampleData,
      selectedItems,
      onItemSelect: (item: any, selected: boolean) => {
        if (selected) {
          setSelectedItems(prev => [...prev, item.id]);
        } else {
          setSelectedItems(prev => prev.filter(id => id !== item.id));
        }
      },
      onItemClick: (item: any) => console.log('Item clicked:', item),
    };

    switch (currentView) {
      case 'list':
        return (
          <ListView
            {...commonProps}
            columns={[
              { key: 'title', label: 'Title', sortable: true },
              { key: 'status', label: 'Status', sortable: true },
              { key: 'priority', label: 'Priority', sortable: true },
              { key: 'assignee', label: 'Assignee', sortable: true },
              { key: 'startDate', label: 'Start Date', sortable: true },
            ]}
            selectable
          />
        );
      
      case 'grid':
        return (
          <GridView
            {...commonProps}
            columns={3}
            selectable
            renderItem={(item) => (
              <div className="p-4">
                <h3 className="font-semibold mb-2">{item.title}</h3>
                <p className="text-sm text-gray-600 mb-2">{item.description}</p>
                <div className="flex justify-between text-xs">
                  <span className="px-2 py-1 bg-blue-100 rounded">{item.status}</span>
                  <span>{item.assignee}</span>
                </div>
              </div>
            )}
          />
        );
      
      case 'card':
        return (
          <CardView
            {...commonProps}
            fields={[
              { key: 'description', label: 'Description' },
              { key: 'status', label: 'Status' },
              { key: 'priority', label: 'Priority' },
              { key: 'assignee', label: 'Assignee' },
            ]}
            selectable
          />
        );
      
      case 'kanban':
        return (
          <KanbanView
            {...commonProps}
            config={viewConfigs.kanban as KanbanViewConfig}
            renderCard={(item) => (
              <div className="p-3">
                <h4 className="font-medium mb-1">{item.title}</h4>
                <p className="text-xs text-gray-600 mb-2">{item.description}</p>
                <div className="flex justify-between items-center text-xs">
                  <span className="px-2 py-1 bg-gray-100 rounded">{item.priority}</span>
                  <span>{item.assignee}</span>
                </div>
              </div>
            )}
          />
        );
      
      case 'calendar':
        return <CalendarView {...commonProps} config={viewConfigs.calendar as CalendarViewConfig} />;
      
      case 'pivot':
        return <PivotView {...commonProps} config={viewConfigs.pivot as PivotViewConfig} />;
      
      case 'graph':
        return <GraphView {...commonProps} config={viewConfigs.graph as GraphViewConfig} />;
      
      case 'activity':
        return <ActivityView {...commonProps} config={viewConfigs.activity as ActivityViewConfig} />;
      
      case 'map':
        return <MapView {...commonProps} config={viewConfigs.map as MapViewConfig} />;
      
      case 'gantt':
        return <GanttView {...commonProps} config={viewConfigs.gantt as GanttViewConfig} />;
      
      case 'hierarchical':
        return <HierarchicalView {...commonProps} config={viewConfigs.hierarchical as HierarchicalViewConfig} />;
      
      case 'relationship':
        return <RelationshipView {...commonProps} config={viewConfigs.relationship as RelationshipViewConfig} />;
      
      case 'form':
        return (
          <FormView
            {...commonProps}
            config={viewConfigs.form as FormViewConfig}
            record={sampleData[0]}
            onSave={(data) => console.log('Save:', data)}
            onCancel={() => console.log('Cancel')}
          />
        );
      
      default:
        return <div className="p-8 text-center">View not implemented</div>;
    }
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: colors.background }}>
      <DynamicAppHeader {...headerProps} />
      
      {/* View selector */}
      <div className="border-b p-4" style={{ borderBottomColor: colors.border }}>
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap gap-2">
            {viewModes.map(mode => (
              <button
                key={mode.id}
                onClick={() => setCurrentView(mode.id)}
                className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                  currentView === mode.id 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-gray-100 hover:bg-gray-200'
                }`}
              >
                {mode.icon} {mode.name}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Current view */}
      <main className="max-w-7xl mx-auto p-6">
        {renderCurrentView()}
      </main>
    </div>
  );
}
