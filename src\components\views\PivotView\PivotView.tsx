import React, { useMemo } from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView } from '../BaseView';
import type { BaseViewProps } from '../BaseView';
import type { PivotViewConfig } from '../../../types/views';
import { cn } from '../../../utils/cn';

export interface PivotData {
  [key: string]: any;
}

export interface PivotViewProps extends BaseViewProps {
  config: PivotViewConfig;
  onCellClick?: (rowKey: string, colKey: string, value: any) => void;
  onRowExpand?: (rowKey: string, expanded: boolean) => void;
  expandedRows?: string[];
  renderCell?: (value: any, rowData: any, colData: any) => React.ReactNode;
}

export const PivotView: React.FC<PivotViewProps> = ({
  data,
  config,
  onCellClick,
  onRowExpand,
  expandedRows = [],
  renderCell,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();

  // Process data into pivot structure
  const pivotData = useMemo(() => {
    const result: { [key: string]: { [key: string]: any } } = {};
    const rowTotals: { [key: string]: number } = {};
    const colTotals: { [key: string]: number } = {};
    let grandTotal = 0;

    // Initialize structure
    const rowKeys = new Set<string>();
    const colKeys = new Set<string>();

    // First pass: collect all unique row and column keys
    data.forEach(item => {
      const rowKey = config.rows.map(field => item[field]).join(' | ');
      const colKey = config.columns.map(field => item[field]).join(' | ');
      rowKeys.add(rowKey);
      colKeys.add(colKey);
    });

    // Initialize result structure
    rowKeys.forEach(rowKey => {
      result[rowKey] = {};
      rowTotals[rowKey] = 0;
      colKeys.forEach(colKey => {
        result[rowKey][colKey] = { count: 0, values: {} };
      });
    });

    colKeys.forEach(colKey => {
      colTotals[colKey] = 0;
    });

    // Second pass: aggregate data
    data.forEach(item => {
      const rowKey = config.rows.map(field => item[field]).join(' | ');
      const colKey = config.columns.map(field => item[field]).join(' | ');

      result[rowKey][colKey].count++;

      config.values.forEach(valueConfig => {
        const value = parseFloat(item[valueConfig.field]) || 0;
        
        if (!result[rowKey][colKey].values[valueConfig.field]) {
          result[rowKey][colKey].values[valueConfig.field] = {
            sum: 0,
            count: 0,
            min: value,
            max: value,
          };
        }

        const cellData = result[rowKey][colKey].values[valueConfig.field];
        cellData.sum += value;
        cellData.count++;
        cellData.min = Math.min(cellData.min, value);
        cellData.max = Math.max(cellData.max, value);

        // Update totals
        if (valueConfig.aggregation === 'sum') {
          rowTotals[rowKey] += value;
          colTotals[colKey] += value;
          grandTotal += value;
        }
      });
    });

    return {
      data: result,
      rowKeys: Array.from(rowKeys),
      colKeys: Array.from(colKeys),
      rowTotals,
      colTotals,
      grandTotal,
    };
  }, [data, config]);

  const getCellValue = (rowKey: string, colKey: string, valueConfig: any) => {
    const cellData = pivotData.data[rowKey]?.[colKey];
    if (!cellData || !cellData.values[valueConfig.field]) return 0;

    const values = cellData.values[valueConfig.field];
    switch (valueConfig.aggregation) {
      case 'sum':
        return values.sum;
      case 'count':
        return values.count;
      case 'avg':
        return values.count > 0 ? values.sum / values.count : 0;
      case 'min':
        return values.min;
      case 'max':
        return values.max;
      default:
        return values.sum;
    }
  };

  const formatValue = (value: number, aggregation: string) => {
    if (aggregation === 'count') {
      return value.toString();
    }
    return value.toLocaleString(undefined, { 
      minimumFractionDigits: 0, 
      maximumFractionDigits: 2 
    });
  };

  const renderHeaderRow = () => (
    <thead>
      <tr style={{ backgroundColor: colors.surface }}>
        {/* Row headers */}
        {config.rows.map((field, index) => (
          <th
            key={field}
            className="px-4 py-3 text-left font-medium border-r"
            style={{ 
              color: colors.text,
              borderRightColor: colors.border,
              borderBottomColor: colors.border 
            }}
          >
            {field}
          </th>
        ))}
        
        {/* Column headers */}
        {pivotData.colKeys.map(colKey => (
          <th
            key={colKey}
            className="px-4 py-3 text-center font-medium border-r"
            style={{ 
              color: colors.text,
              borderRightColor: colors.border,
              borderBottomColor: colors.border 
            }}
          >
            {colKey}
          </th>
        ))}
        
        {/* Total column */}
        {config.showTotals && (
          <th
            className="px-4 py-3 text-center font-medium"
            style={{ 
              color: colors.text,
              borderBottomColor: colors.border 
            }}
          >
            Total
          </th>
        )}
      </tr>
    </thead>
  );

  const renderDataRows = () => (
    <tbody>
      {pivotData.rowKeys.map((rowKey, rowIndex) => (
        <tr
          key={rowKey}
          className={cn(
            'border-b hover:bg-opacity-50',
            rowIndex % 2 === 1 && 'bg-opacity-25'
          )}
          style={{ 
            borderBottomColor: colors.border,
            backgroundColor: rowIndex % 2 === 1 ? colors.muted : 'transparent'
          }}
        >
          {/* Row data */}
          {config.rows.map((field, fieldIndex) => {
            const rowParts = rowKey.split(' | ');
            return (
              <td
                key={field}
                className="px-4 py-3 border-r font-medium"
                style={{ 
                  color: colors.text,
                  borderRightColor: colors.border 
                }}
              >
                {rowParts[fieldIndex] || ''}
              </td>
            );
          })}
          
          {/* Data cells */}
          {pivotData.colKeys.map(colKey => (
            <td
              key={colKey}
              className="px-4 py-3 text-center border-r cursor-pointer hover:bg-gray-100"
              style={{ borderRightColor: colors.border }}
              onClick={() => onCellClick?.(rowKey, colKey, pivotData.data[rowKey][colKey])}
            >
              {config.values.map((valueConfig, index) => {
                const value = getCellValue(rowKey, colKey, valueConfig);
                return (
                  <div key={valueConfig.field} style={{ color: colors.text }}>
                    {renderCell ? 
                      renderCell(value, { rowKey }, { colKey }) : 
                      formatValue(value, valueConfig.aggregation)
                    }
                    {config.values.length > 1 && (
                      <div className="text-xs" style={{ color: colors.textSecondary }}>
                        {valueConfig.label || valueConfig.field}
                      </div>
                    )}
                  </div>
                );
              })}
            </td>
          ))}
          
          {/* Row total */}
          {config.showTotals && (
            <td
              className="px-4 py-3 text-center font-semibold"
              style={{ color: colors.text }}
            >
              {formatValue(pivotData.rowTotals[rowKey], 'sum')}
            </td>
          )}
        </tr>
      ))}
      
      {/* Column totals row */}
      {config.showTotals && (
        <tr 
          className="font-semibold border-t-2"
          style={{ 
            backgroundColor: colors.surface,
            borderTopColor: colors.border 
          }}
        >
          <td 
            colSpan={config.rows.length}
            className="px-4 py-3"
            style={{ color: colors.text }}
          >
            Total
          </td>
          {pivotData.colKeys.map(colKey => (
            <td
              key={colKey}
              className="px-4 py-3 text-center"
              style={{ color: colors.text }}
            >
              {formatValue(pivotData.colTotals[colKey], 'sum')}
            </td>
          ))}
          <td
            className="px-4 py-3 text-center"
            style={{ color: colors.text }}
          >
            {formatValue(pivotData.grandTotal, 'sum')}
          </td>
        </tr>
      )}
    </tbody>
  );

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div className="overflow-auto">
        <table className="w-full border-collapse">
          {renderHeaderRow()}
          {renderDataRows()}
        </table>
      </div>
    </BaseView>
  );
};

export default PivotView;
