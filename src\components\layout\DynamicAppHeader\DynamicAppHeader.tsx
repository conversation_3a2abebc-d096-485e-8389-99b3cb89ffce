import React, { useState } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { Button, Input } from '../../ui';
import { cn } from '../../../utils/cn';

// Icon Components
const MenuIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M4 6h16M4 12h16M4 18h16"
    />
  </svg>
);

const SearchIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
    />
  </svg>
);

const BellIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
    />
  </svg>
);

const ChevronLeftIcon = () => (
  <svg
    className="w-4 h-4"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15 19l-7-7 7-7"
    />
  </svg>
);

const ChevronRightIcon = () => (
  <svg
    className="w-4 h-4"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 5l7 7-7 7"
    />
  </svg>
);

const ListIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M4 6h16M4 10h16M4 14h16M4 18h16"
    />
  </svg>
);

const GridIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
    />
  </svg>
);

const BarChartIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
    />
  </svg>
);

const MoreVerticalIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
    />
  </svg>
);

const PlusIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 4v16m8-8H4"
    />
  </svg>
);

const UploadIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
    />
  </svg>
);

const XIcon = () => (
  <svg
    className="w-4 h-4"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M6 18L18 6M6 6l12 12"
    />
  </svg>
);

// Type Definitions
export interface DynamicAppHeaderProps {
  app: {
    name: string;
    icon: React.ReactNode;
    navLinks: { label: string; href: string; isActive?: boolean }[];
  };
  user: {
    name: string;
    avatar: React.ReactNode;
    notifications: { count: number; icon: React.ReactNode }[];
  };
  view: {
    title: string;
    actions: { label: string; onClick: () => void; isPrimary?: boolean }[];
    search: {
      filters: { id: any; label: string }[];
      onSearch: (query: string) => void;
      onRemoveFilter: (id: any) => void;
    };
    pagination: {
      currentRange: string;
      onNext: () => void;
      onPrev: () => void;
    };
    viewModes: { name: string; icon: React.ReactNode }[];
    activeViewMode: string;
  };
  className?: string;
  'data-testid'?: string;
}

const DynamicAppHeader: React.FC<DynamicAppHeaderProps> = ({
  app,
  user,
  view,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const headerStyle = {
    backgroundColor: colors.surface,
    borderColor: colors.border,
  };

  const activeNavStyle = {
    color: colors.primary,
    borderColor: colors.primary,
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    view.search.onSearch(searchQuery);
  };

  return (
    <header
      className={cn('border-b', className)}
      style={headerStyle}
      data-testid={testId}
    >
      {/* Top Bar - Global Navigation */}
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-14">
          {/* Left: App Icon, Name, and Navigation */}
          <div className="flex items-center space-x-6">
            {/* App Icon and Name */}
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 flex items-center justify-center">
                {app.icon}
              </div>
              <h1
                className="text-lg font-semibold"
                style={{ color: colors.text }}
              >
                {app.name}
              </h1>
            </div>

            {/* Desktop Navigation Links */}
            <nav className="hidden md:flex items-center space-x-6">
              {app.navLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  className={cn(
                    'px-3 py-2 text-sm font-medium border-b-2 transition-colors duration-200',
                    link.isActive
                      ? 'border-current'
                      : 'border-transparent hover:border-current/50'
                  )}
                  style={
                    link.isActive
                      ? activeNavStyle
                      : { color: colors.mutedForeground }
                  }
                >
                  {link.label}
                </a>
              ))}
            </nav>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2 rounded-md hover:bg-opacity-10"
              style={{ backgroundColor: colors.hover }}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              <MenuIcon />
            </button>
          </div>

          {/* Right: System & User Tray */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            {user.notifications.map((notification, index) => (
              <button
                key={index}
                className="relative p-2 rounded-full hover:bg-opacity-10 transition-colors duration-200"
                style={{ backgroundColor: colors.hover }}
                aria-label={`Notifications (${notification.count})`}
              >
                {notification.icon}
                {notification.count > 0 && (
                  <span
                    className="absolute -top-1 -right-1 w-5 h-5 text-xs font-bold rounded-full flex items-center justify-center"
                    style={{
                      backgroundColor: colors.error,
                      color: colors.errorForeground,
                    }}
                  >
                    {notification.count > 99 ? '99+' : notification.count}
                  </span>
                )}
              </button>
            ))}

            {/* User Info */}
            <div className="flex items-center space-x-3">
              <span
                className="hidden sm:block text-sm font-medium"
                style={{ color: colors.text }}
              >
                {user.name}
              </span>
              <div className="w-8 h-8 rounded-full overflow-hidden">
                {user.avatar}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div
          className="md:hidden border-t"
          style={{ borderColor: colors.border }}
        >
          <div className="px-4 py-3 space-y-2">
            {app.navLinks.map((link, index) => (
              <a
                key={index}
                href={link.href}
                className={cn(
                  'block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200',
                  link.isActive ? 'bg-opacity-10' : 'hover:bg-opacity-5'
                )}
                style={{
                  color: link.isActive ? colors.primary : colors.text,
                  backgroundColor: link.isActive
                    ? colors.primary + '1A'
                    : 'transparent',
                }}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {link.label}
              </a>
            ))}
          </div>
        </div>
      )}

      {/* Bottom Bar - Contextual Controls */}
      <div className="border-t" style={{ borderColor: colors.border }}>
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left: Action Buttons and View Title */}
            <div className="flex items-center space-x-4">
              {/* Action Buttons - Desktop */}
              <div className="hidden sm:flex items-center space-x-2">
                {view.actions.map((action, index) => (
                  <Button
                    key={index}
                    variant={action.isPrimary ? 'primary' : 'outline'}
                    size="sm"
                    onClick={action.onClick}
                    className="flex items-center space-x-2"
                  >
                    {action.label === 'New' && <PlusIcon />}
                    {action.label === 'Upload' && <UploadIcon />}
                    <span>{action.label}</span>
                  </Button>
                ))}
              </div>

              {/* View Title */}
              <div className="flex items-center">
                <h2
                  className="text-lg font-semibold"
                  style={{ color: colors.text }}
                >
                  {view.title}
                </h2>
              </div>
            </div>

            {/* Center: Search Bar */}
            <div className="flex-1 max-w-2xl mx-8 hidden lg:block">
              <form onSubmit={handleSearchSubmit} className="relative">
                <div className="flex items-center space-x-2">
                  {/* Search Input */}
                  <div className="flex-1 relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <SearchIcon />
                    </div>
                    <Input
                      type="text"
                      placeholder="Search..."
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full"
                      style={{
                        backgroundColor: colors.background,
                        borderColor: colors.border,
                        color: colors.text,
                      }}
                    />
                  </div>

                  {/* Filter Pills */}
                  {view.search.filters.length > 0 && (
                    <div className="flex items-center space-x-2">
                      {view.search.filters.map(filter => (
                        <div
                          key={filter.id}
                          className="flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium"
                          style={{
                            backgroundColor: colors.primary + '1A',
                            color: colors.primary,
                          }}
                        >
                          <span>{filter.label}</span>
                          <button
                            onClick={() =>
                              view.search.onRemoveFilter(filter.id)
                            }
                            className="hover:bg-opacity-20 rounded-full p-0.5"
                            aria-label={`Remove ${filter.label} filter`}
                          >
                            <XIcon />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </form>
            </div>

            {/* Right: Pagination and View Switcher */}
            <div className="flex items-center space-x-4">
              {/* Pagination - Desktop */}
              <div className="hidden md:flex items-center space-x-2">
                <button
                  onClick={view.pagination.onPrev}
                  className="p-1 rounded hover:bg-opacity-10 transition-colors duration-200"
                  style={{ backgroundColor: colors.hover }}
                  aria-label="Previous page"
                >
                  <ChevronLeftIcon />
                </button>
                <span
                  className="text-sm font-medium px-2"
                  style={{ color: colors.text }}
                >
                  {view.pagination.currentRange}
                </span>
                <button
                  onClick={view.pagination.onNext}
                  className="p-1 rounded hover:bg-opacity-10 transition-colors duration-200"
                  style={{ backgroundColor: colors.hover }}
                  aria-label="Next page"
                >
                  <ChevronRightIcon />
                </button>
              </div>

              {/* View Mode Switcher - Desktop */}
              <div
                className="hidden md:flex items-center space-x-1 p-1 rounded-lg"
                style={{ backgroundColor: colors.muted }}
              >
                {view.viewModes.map(mode => (
                  <button
                    key={mode.name}
                    className={cn(
                      'p-2 rounded-md transition-all duration-200',
                      mode.name === view.activeViewMode
                        ? 'shadow-sm'
                        : 'hover:bg-opacity-10'
                    )}
                    style={{
                      backgroundColor:
                        mode.name === view.activeViewMode
                          ? colors.background
                          : 'transparent',
                      color:
                        mode.name === view.activeViewMode
                          ? colors.primary
                          : colors.mutedForeground,
                    }}
                    aria-label={`Switch to ${mode.name} view`}
                    title={`${mode.name} view`}
                  >
                    {mode.icon}
                  </button>
                ))}
              </div>

              {/* Mobile Search Toggle */}
              <button
                className="lg:hidden p-2 rounded-md hover:bg-opacity-10 transition-colors duration-200"
                style={{ backgroundColor: colors.hover }}
                onClick={() => setIsSearchExpanded(!isSearchExpanded)}
                aria-label="Toggle search"
              >
                <SearchIcon />
              </button>

              {/* Mobile Menu Button */}
              <button
                className="md:hidden p-2 rounded-md hover:bg-opacity-10 transition-colors duration-200"
                style={{ backgroundColor: colors.hover }}
                aria-label="More options"
              >
                <MoreVerticalIcon />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Search Overlay */}
      {isSearchExpanded && (
        <div
          className="lg:hidden border-t"
          style={{ borderColor: colors.border }}
        >
          <div className="p-4">
            <form onSubmit={handleSearchSubmit} className="space-y-3">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <SearchIcon />
                </div>
                <Input
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full"
                  style={{
                    backgroundColor: colors.background,
                    borderColor: colors.border,
                    color: colors.text,
                  }}
                />
              </div>

              {/* Mobile Filter Pills */}
              {view.search.filters.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {view.search.filters.map(filter => (
                    <div
                      key={filter.id}
                      className="flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium"
                      style={{
                        backgroundColor: colors.primary + '1A',
                        color: colors.primary,
                      }}
                    >
                      <span>{filter.label}</span>
                      <button
                        onClick={() => view.search.onRemoveFilter(filter.id)}
                        className="hover:bg-opacity-20 rounded-full p-0.5"
                        aria-label={`Remove ${filter.label} filter`}
                      >
                        <XIcon />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </form>
          </div>
        </div>
      )}

      {/* Floating Action Button - Mobile */}
      <div className="sm:hidden fixed bottom-6 right-6 z-50">
        {view.actions
          .filter(action => action.isPrimary)
          .map((action, index) => (
            <button
              key={index}
              onClick={action.onClick}
              className="w-14 h-14 rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-105"
              style={{
                backgroundColor: colors.primary,
                color: colors.primaryForeground,
              }}
              aria-label={action.label}
            >
              {action.label === 'New' && <PlusIcon />}
              {action.label === 'Upload' && <UploadIcon />}
            </button>
          ))}
      </div>
    </header>
  );
};

export default DynamicAppHeader;
