import React from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView } from '../BaseView';
import type { BaseViewProps } from '../BaseView';
import { cn } from '../../../utils/cn';

export interface ListViewColumn {
  key: string;
  label: string;
  width?: string | number;
  sortable?: boolean;
  render?: (value: any, item: any, index: number) => React.ReactNode;
  align?: 'left' | 'center' | 'right';
  className?: string;
}

export interface ListViewProps extends BaseViewProps {
  columns: ListViewColumn[];
  showHeader?: boolean;
  striped?: boolean;
  hoverable?: boolean;
  selectable?: boolean;
  selectedItems?: string[];
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
  rowKey?: string | ((item: any) => string);
  onRowClick?: (item: any, index: number) => void;
  onRowSelect?: (item: any, selected: boolean) => void;
  renderRowActions?: (item: any, index: number) => React.ReactNode;
}

export const ListView: React.FC<ListViewProps> = ({
  data,
  columns,
  showHeader = true,
  striped = true,
  hoverable = true,
  selectable = false,
  selectedItems = [],
  onSort,
  sortColumn,
  sortDirection,
  rowKey = 'id',
  onRowClick,
  onRowSelect,
  renderRowActions,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();

  const getRowKey = (item: any, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(item);
    }
    return item[rowKey] || index.toString();
  };

  const isSelected = (item: any): boolean => {
    const key = getRowKey(item, 0);
    return selectedItems.includes(key);
  };

  const handleSort = (column: ListViewColumn) => {
    if (!column.sortable || !onSort) return;
    
    const newDirection = sortColumn === column.key && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(column.key, newDirection);
  };

  const handleRowSelect = (item: any, event: React.ChangeEvent<HTMLInputElement>) => {
    if (onRowSelect) {
      onRowSelect(item, event.target.checked);
    }
  };

  const renderCell = (column: ListViewColumn, item: any, index: number) => {
    const value = item[column.key];
    
    if (column.render) {
      return column.render(value, item, index);
    }
    
    return value?.toString() || '';
  };

  const renderHeader = () => {
    if (!showHeader) return null;

    return (
      <thead>
        <tr style={{ backgroundColor: colors.surface, borderBottomColor: colors.border }}>
          {selectable && (
            <th className="px-4 py-3 text-left">
              <input
                type="checkbox"
                className="rounded"
                onChange={(e) => {
                  // Handle select all
                  data.forEach(item => {
                    if (onRowSelect) {
                      onRowSelect(item, e.target.checked);
                    }
                  });
                }}
              />
            </th>
          )}
          {columns.map((column) => (
            <th
              key={column.key}
              className={cn(
                'px-4 py-3 text-left font-medium',
                column.sortable && 'cursor-pointer hover:bg-opacity-80',
                column.className
              )}
              style={{ 
                color: colors.text,
                width: column.width,
                textAlign: column.align || 'left'
              }}
              onClick={() => handleSort(column)}
            >
              <div className="flex items-center space-x-1">
                <span>{column.label}</span>
                {column.sortable && sortColumn === column.key && (
                  <span className="text-xs">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </div>
            </th>
          ))}
          {renderRowActions && (
            <th className="px-4 py-3 text-right font-medium" style={{ color: colors.text }}>
              Actions
            </th>
          )}
        </tr>
      </thead>
    );
  };

  const renderBody = () => (
    <tbody>
      {data.map((item, index) => {
        const key = getRowKey(item, index);
        const selected = isSelected(item);
        
        return (
          <tr
            key={key}
            className={cn(
              'border-b transition-colors',
              hoverable && 'hover:bg-opacity-50',
              striped && index % 2 === 1 && 'bg-opacity-25',
              selected && 'bg-blue-50 dark:bg-blue-900/20',
              onRowClick && 'cursor-pointer'
            )}
            style={{ 
              borderBottomColor: colors.border,
              backgroundColor: striped && index % 2 === 1 ? colors.muted : 'transparent'
            }}
            onClick={() => onRowClick?.(item, index)}
          >
            {selectable && (
              <td className="px-4 py-3">
                <input
                  type="checkbox"
                  className="rounded"
                  checked={selected}
                  onChange={(e) => handleRowSelect(item, e)}
                  onClick={(e) => e.stopPropagation()}
                />
              </td>
            )}
            {columns.map((column) => (
              <td
                key={column.key}
                className={cn('px-4 py-3', column.className)}
                style={{ 
                  color: colors.text,
                  textAlign: column.align || 'left'
                }}
              >
                {renderCell(column, item, index)}
              </td>
            ))}
            {renderRowActions && (
              <td className="px-4 py-3 text-right">
                <div onClick={(e) => e.stopPropagation()}>
                  {renderRowActions(item, index)}
                </div>
              </td>
            )}
          </tr>
        );
      })}
    </tbody>
  );

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          {renderHeader()}
          {renderBody()}
        </table>
      </div>
    </BaseView>
  );
};

export default ListView;
