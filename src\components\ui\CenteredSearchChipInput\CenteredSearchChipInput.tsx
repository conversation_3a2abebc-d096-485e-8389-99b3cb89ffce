import React, { useState, useRef, useEffect } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

// Icons
const SearchIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const XIcon = () => (
  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
);

const StarIcon = ({ filled = false }: { filled?: boolean }) => (
  <svg className="w-4 h-4" fill={filled ? "currentColor" : "none"} stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
  </svg>
);

const FilterIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
  </svg>
);

const GroupIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
  </svg>
);

const TrashIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
  </svg>
);

const ChevronDownIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
  </svg>
);

const CheckIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
  </svg>
);

export interface FilterTag {
  id: string;
  label: string;
  removable?: boolean;
}

export interface FilterItem {
  id: string;
  label: string;
  selected?: boolean;
  hasDropdown?: boolean;
  onClick?: () => void;
}

export interface FavoriteItem {
  id: string;
  label: string;
  selected?: boolean;
  onDelete?: () => void;
  onClick?: () => void;
}

export interface GroupByItem {
  id: string;
  label: string;
  hasDropdown?: boolean;
  onClick?: () => void;
}

export interface CenteredSearchChipInputProps {
  placeholder?: string;
  filterTags?: FilterTag[];
  filterItems?: FilterItem[];
  groupByItems?: GroupByItem[];
  favoriteItems?: FavoriteItem[];
  onSearch?: (query: string) => void;
  onTagRemove?: (tagId: string) => void;
  onFilterSelect?: (filterId: string) => void;
  onGroupBySelect?: (groupId: string) => void;
  onFavoriteSelect?: (favoriteId: string) => void;
  onFavoriteDelete?: (favoriteId: string) => void;
  onAddCustomFilter?: () => void;
  onAddCustomGroup?: () => void;
  onSaveCurrentSearch?: () => void;
  className?: string;
  'data-testid'?: string;
}

const CenteredSearchChipInput: React.FC<CenteredSearchChipInputProps> = ({
  placeholder = "Search...",
  filterTags = [],
  filterItems = [],
  groupByItems = [],
  favoriteItems = [],
  onSearch,
  onTagRemove,
  onFilterSelect,
  onGroupBySelect,
  onFavoriteSelect,
  onFavoriteDelete,
  onAddCustomFilter,
  onAddCustomGroup,
  onSaveCurrentSearch,
  className = '',
  'data-testid': testId,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { colors } = useThemeStore();
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    onSearch?.(value);
  };

  const handleInputFocus = () => {
    setIsDropdownOpen(true);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(searchQuery);
  };

  const handleTagRemove = (tagId: string) => {
    onTagRemove?.(tagId);
  };

  // Odoo-style color scheme based on app theme
  const odooColors = {
    // Primary background color: #2C2C39 equivalent in app theme
    primaryBg: colors.surface, // #1f2937 in dark theme
    // Primary text color: #FFFFFF
    primaryText: colors.text, // #f9fafb in dark theme
    // Secondary text color: #B0B0B0
    secondaryText: colors.textMuted, // #9ca3af in dark theme
    // Accent Colors
    filtersIcon: '#E91E63', // pink
    groupByIcon: '#00BCD4', // cyan
    favoritesIcon: '#FFC107', // yellow
    // Hover background color: #3C3C4F equivalent
    hoverBg: colors.hover, // #374151 in dark theme
    // Border color / Dividers: #41415B equivalent
    borderColor: colors.border, // #4b5563 in dark theme
    // Tag background color: #4E4E68 equivalent
    tagBg: colors.surfaceTertiary, // #4b5563 in dark theme
    // Trash icon hover color
    trashHover: colors.error, // #ef4444 in dark theme
  };

  return (
    <div className={cn("relative w-full max-w-4xl mx-auto", className)} data-testid={testId} ref={dropdownRef}>
      <form onSubmit={handleSearchSubmit} className="relative">
        {/* Search Bar with Filter Tags */}
        <div
          className="flex items-center min-h-[40px] px-3 py-2 border rounded transition-all duration-200 focus-within:ring-2 focus-within:ring-offset-1"
          style={{
            backgroundColor: odooColors.primaryBg,
            borderColor: odooColors.borderColor,
            color: odooColors.primaryText,
          }}
        >
          {/* Filter Tags */}
          <div className="flex items-center gap-2 flex-wrap">
            {filterTags.map((tag) => (
              <div
                key={tag.id}
                className="inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium transition-colors"
                style={{
                  backgroundColor: odooColors.tagBg,
                  color: odooColors.primaryText,
                }}
              >
                <span className="truncate max-w-[120px]">{tag.label}</span>
                {tag.removable && (
                  <button
                    type="button"
                    onClick={() => handleTagRemove(tag.id)}
                    className="ml-1 hover:opacity-70 rounded p-0.5 transition-opacity"
                    aria-label={`Remove ${tag.label}`}
                  >
                    <XIcon />
                  </button>
                )}
              </div>
            ))}

            {/* Search Input */}
            <input
              ref={inputRef}
              type="text"
              value={searchQuery}
              onChange={handleSearchChange}
              onFocus={handleInputFocus}
              placeholder={placeholder}
              className="flex-1 min-w-[120px] bg-transparent border-none outline-none text-sm placeholder-opacity-60"
              style={{
                color: odooColors.primaryText,
              }}
            />
          </div>

          {/* Dropdown Arrow */}
          <div className="flex-shrink-0 ml-2" style={{ color: odooColors.secondaryText }}>
            <ChevronDownIcon />
          </div>
        </div>

        {/* Odoo-style Dropdown */}
        {isDropdownOpen && (
          <div
            className="absolute top-full left-0 right-0 mt-1 border rounded z-50 overflow-hidden"
            style={{
              backgroundColor: odooColors.primaryBg,
              borderColor: odooColors.borderColor,
              boxShadow: `0 4px 6px -1px ${colors.shadow}`,
              width: '750px',
              maxWidth: '100vw',
            }}
          >
            {/* Three-column layout */}
            <div className="flex" style={{ minHeight: '300px' }}>
              {/* Filters Section */}
              <div className="flex-1 p-4 border-r" style={{ borderColor: odooColors.borderColor }}>
                <div className="flex items-center gap-2 mb-3">
                  <div style={{ color: odooColors.filtersIcon }}>
                    <FilterIcon />
                  </div>
                  <h3 className="text-sm font-bold" style={{ color: odooColors.primaryText }}>
                    Filters
                  </h3>
                </div>
                <ul className="space-y-1">
                  {filterItems.map((item) => (
                    <li key={item.id}>
                      <button
                        type="button"
                        onClick={() => onFilterSelect?.(item.id)}
                        className="w-full text-left px-2 py-1.5 text-sm rounded transition-colors flex items-center justify-between group"
                        style={{
                          color: odooColors.primaryText,
                          backgroundColor: item.selected ? odooColors.hoverBg : 'transparent',
                        }}
                        onMouseEnter={(e) => {
                          if (!item.selected) {
                            e.currentTarget.style.backgroundColor = odooColors.hoverBg;
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!item.selected) {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }
                        }}
                      >
                        <span className="flex items-center gap-2">
                          {item.selected && (
                            <div style={{ color: odooColors.filtersIcon }}>
                              <CheckIcon />
                            </div>
                          )}
                          {item.label}
                        </span>
                        {item.hasDropdown && (
                          <div style={{ color: odooColors.secondaryText }}>
                            <ChevronDownIcon />
                          </div>
                        )}
                      </button>
                    </li>
                  ))}
                </ul>
                <button
                  type="button"
                  onClick={onAddCustomFilter}
                  className="mt-3 text-sm transition-colors"
                  style={{ color: odooColors.filtersIcon }}
                >
                  Add Custom Filter
                </button>
              </div>

              {/* Group By Section */}
              <div className="flex-1 p-4 border-r" style={{ borderColor: odooColors.borderColor }}>
                <div className="flex items-center gap-2 mb-3">
                  <div style={{ color: odooColors.groupByIcon }}>
                    <GroupIcon />
                  </div>
                  <h3 className="text-sm font-bold" style={{ color: odooColors.primaryText }}>
                    Group By
                  </h3>
                </div>
                <ul className="space-y-1">
                  {groupByItems.map((item) => (
                    <li key={item.id}>
                      <button
                        type="button"
                        onClick={() => onGroupBySelect?.(item.id)}
                        className="w-full text-left px-2 py-1.5 text-sm rounded transition-colors flex items-center justify-between group"
                        style={{ color: odooColors.primaryText }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = odooColors.hoverBg;
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                      >
                        <span>{item.label}</span>
                        {item.hasDropdown && (
                          <div style={{ color: odooColors.secondaryText }}>
                            <ChevronDownIcon />
                          </div>
                        )}
                      </button>
                    </li>
                  ))}
                </ul>
                <button
                  type="button"
                  onClick={onAddCustomGroup}
                  className="mt-3 text-sm transition-colors"
                  style={{ color: odooColors.groupByIcon }}
                >
                  Add Custom Group
                </button>
              </div>

              {/* Favorites Section */}
              <div className="flex-1 p-4">
                <div className="flex items-center gap-2 mb-3">
                  <div style={{ color: odooColors.favoritesIcon }}>
                    <StarIcon filled />
                  </div>
                  <h3 className="text-sm font-bold" style={{ color: odooColors.primaryText }}>
                    Favorites
                  </h3>
                </div>
                <ul className="space-y-1">
                  {favoriteItems.map((item) => (
                    <li key={item.id}>
                      <div className="flex items-center justify-between group">
                        <button
                          type="button"
                          onClick={() => onFavoriteSelect?.(item.id)}
                          className="flex-1 text-left px-2 py-1.5 text-sm rounded transition-colors flex items-center gap-2"
                          style={{
                            color: odooColors.primaryText,
                            backgroundColor: item.selected ? odooColors.hoverBg : 'transparent',
                          }}
                          onMouseEnter={(e) => {
                            if (!item.selected) {
                              e.currentTarget.style.backgroundColor = odooColors.hoverBg;
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!item.selected) {
                              e.currentTarget.style.backgroundColor = 'transparent';
                            }
                          }}
                        >
                          {item.selected && (
                            <div style={{ color: odooColors.favoritesIcon }}>
                              <CheckIcon />
                            </div>
                          )}
                          <span>{item.label}</span>
                        </button>
                        <button
                          type="button"
                          onClick={() => onFavoriteDelete?.(item.id)}
                          className="p-1 rounded transition-colors opacity-0 group-hover:opacity-100"
                          style={{ color: odooColors.secondaryText }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.color = odooColors.trashHover;
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.color = odooColors.secondaryText;
                          }}
                        >
                          <TrashIcon />
                        </button>
                      </div>
                    </li>
                  ))}
                </ul>
                <button
                  type="button"
                  onClick={onSaveCurrentSearch}
                  className="mt-3 text-sm transition-colors"
                  style={{ color: odooColors.favoritesIcon }}
                >
                  Save current search
                </button>
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default CenteredSearchChipInput;
